* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.page {
    display: none;
    min-height: 100vh;
}

.page.active {
    display: block;
}

/* Sign Up Page Styles */
.container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
}

.signup-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 450px;
    width: 100%;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.header h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 32px;
    font-weight: 700;
}

.header p {
    color: #666;
    font-size: 16px;
}

.signup-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.error-message {
    color: #e53e3e;
    font-size: 14px;
    margin-top: 5px;
    display: none;
}

.signup-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.signup-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.signup-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.features {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.features h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
}

.features ul {
    list-style: none;
}

.features li {
    padding: 8px 0;
    color: #555;
    font-size: 14px;
}

/* Chat Bot Page Styles */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 20px 20px 0 0;
    overflow: hidden;
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 14px;
}

.logout-btn {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    scroll-behavior: smooth;
}

.message {
    margin-bottom: 20px;
    display: flex;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-message {
    justify-content: flex-end;
}

.bot-message {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 15px 20px;
    border-radius: 20px;
    word-wrap: break-word;
}

.user-message .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 5px;
}

.bot-message .message-content {
    background: white;
    color: #333;
    border: 1px solid #e1e5e9;
    border-bottom-left-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.message-content p {
    margin: 0;
    line-height: 1.5;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    display: block;
    margin-top: 5px;
}

.quick-questions {
    padding: 20px;
    background: white;
    border-top: 1px solid #e1e5e9;
}

.quick-questions p {
    margin: 0 0 15px 0;
    color: #666;
    font-weight: 600;
    text-align: center;
}

.quick-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.quick-btn {
    padding: 8px 16px;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 20px;
    color: #667eea;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
}

.chat-input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e1e5e9;
}

.input-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

#chat-input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 25px;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
}

#chat-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.send-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #667eea;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .signup-card {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .chat-container {
        height: 100vh;
        border-radius: 0;
    }
    
    .header-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .quick-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .quick-btn {
        width: 100%;
        max-width: 300px;
    }
}
