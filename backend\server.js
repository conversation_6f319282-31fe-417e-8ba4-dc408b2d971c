const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-this';

// Middleware
app.use(cors());
app.use(express.json());

// Data storage paths
const USERS_FILE = path.join(__dirname, 'data', 'users.json');
const CHATS_FILE = path.join(__dirname, 'data', 'chats.json');

// Ensure data directory exists
async function ensureDataDirectory() {
  try {
    await fs.mkdir(path.join(__dirname, 'data'), { recursive: true });
    
    // Initialize users file if it doesn't exist
    try {
      await fs.access(USERS_FILE);
    } catch {
      await fs.writeFile(USERS_FILE, JSON.stringify([]));
    }
    
    // Initialize chats file if it doesn't exist
    try {
      await fs.access(CHATS_FILE);
    } catch {
      await fs.writeFile(CHATS_FILE, JSON.stringify([]));
    }
  } catch (error) {
    console.error('Error creating data directory:', error);
  }
}

// Helper functions
async function readUsers() {
  try {
    const data = await fs.readFile(USERS_FILE, 'utf8');
    return JSON.parse(data);
  } catch {
    return [];
  }
}

async function writeUsers(users) {
  await fs.writeFile(USERS_FILE, JSON.stringify(users, null, 2));
}

async function readChats() {
  try {
    const data = await fs.readFile(CHATS_FILE, 'utf8');
    return JSON.parse(data);
  } catch {
    return [];
  }
}

async function writeChats(chats) {
  await fs.writeFile(CHATS_FILE, JSON.stringify(chats, null, 2));
}

// Authentication middleware
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
}

// Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Game Chatbot API is running' });
});

// User registration
app.post('/api/register', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }

    const users = await readUsers();
    
    // Check if user already exists
    if (users.find(user => user.email === email)) {
      return res.status(400).json({ error: 'User already exists' });
    }

    // Create new user
    const newUser = {
      id: Date.now().toString(),
      email,
      createdAt: new Date().toISOString()
    };

    users.push(newUser);
    await writeUsers(users);

    // Generate JWT token
    const token = jwt.sign({ id: newUser.id, email: newUser.email }, JWT_SECRET, { expiresIn: '24h' });

    res.status(201).json({
      message: 'User registered successfully',
      token,
      user: { id: newUser.id, email: newUser.email }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// User login
app.post('/api/login', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    const users = await readUsers();
    const user = users.find(u => u.email === email);

    if (!user) {
      return res.status(400).json({ error: 'User not found' });
    }

    // Generate JWT token
    const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, { expiresIn: '24h' });

    res.json({
      message: 'Login successful',
      token,
      user: { id: user.id, email: user.email }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user profile
app.get('/api/profile', authenticateToken, (req, res) => {
  res.json({ user: req.user });
});

// Chat endpoint
app.post('/api/chat', authenticateToken, async (req, res) => {
  try {
    const { message } = req.body;
    const userId = req.user.id;

    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Simple AI response logic (can be enhanced with actual AI API)
    const aiResponse = generateGameResponse(message);

    // Save chat to history
    const chats = await readChats();
    const newChat = {
      id: Date.now().toString(),
      userId,
      userMessage: message,
      aiResponse,
      timestamp: new Date().toISOString()
    };

    chats.push(newChat);
    await writeChats(chats);

    res.json({
      response: aiResponse,
      chatId: newChat.id
    });
  } catch (error) {
    console.error('Chat error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get chat history
app.get('/api/chat/history', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const chats = await readChats();
    const userChats = chats.filter(chat => chat.userId === userId);
    
    res.json({ chats: userChats });
  } catch (error) {
    console.error('Chat history error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Simple AI response generator (placeholder for actual AI integration)
function generateGameResponse(message) {
  const lowerMessage = message.toLowerCase();
  
  // Game recommendations based on keywords
  if (lowerMessage.includes('recommend') || lowerMessage.includes('suggest')) {
    return "I'd recommend some great games! For action: Call of Duty, Apex Legends. For RPG: The Witcher 3, Skyrim. For strategy: Civilization VI, Age of Empires. What genre interests you most?";
  }
  
  if (lowerMessage.includes('fps') || lowerMessage.includes('shooter')) {
    return "Great FPS games include: Counter-Strike 2, Valorant, Call of Duty series, Battlefield series, and Overwatch 2. Which type of shooter do you prefer - tactical, battle royale, or arcade?";
  }
  
  if (lowerMessage.includes('rpg') || lowerMessage.includes('role playing')) {
    return "Amazing RPGs to try: The Witcher 3, Cyberpunk 2077, Elden Ring, Final Fantasy series, and Baldur's Gate 3. Do you prefer fantasy or sci-fi settings?";
  }
  
  if (lowerMessage.includes('strategy')) {
    return "Top strategy games: Civilization VI, Total War series, StarCraft II, Age of Empires IV, and Europa Universalis IV. Are you interested in turn-based or real-time strategy?";
  }
  
  if (lowerMessage.includes('indie')) {
    return "Fantastic indie games: Hades, Celeste, Hollow Knight, Stardew Valley, and Among Us. What type of indie experience are you looking for?";
  }
  
  if (lowerMessage.includes('multiplayer')) {
    return "Great multiplayer games: Fortnite, Minecraft, Rocket League, Fall Guys, and League of Legends. Do you prefer competitive or cooperative multiplayer?";
  }
  
  // Default response
  return "I'm your gaming assistant! I can help you discover new games, get recommendations, learn about different genres, and find games that match your preferences. What kind of gaming experience are you looking for today?";
}

// Start server
async function startServer() {
  await ensureDataDirectory();
  app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
  });
}

startServer();
