// Game Bot AI - JavaScript Functionality

class GameBotAI {
    constructor() {
        this.currentUser = null;
        this.chatHistory = [];
        this.isTyping = false;
        
        this.init();
    }
    
    init() {
        // Check if user is already logged in
        const savedUser = localStorage.getItem('gameBotUser');
        if (savedUser) {
            this.currentUser = JSON.parse(savedUser);
            this.showChatPage();
        } else {
            this.showSignupPage();
        }
        
        this.bindEvents();
    }
    
    bindEvents() {
        // Signup form
        const signupForm = document.getElementById('signup-form');
        if (signupForm) {
            signupForm.addEventListener('submit', (e) => this.handleSignup(e));
        }
        
        // Chat form
        const chatForm = document.getElementById('chat-form');
        if (chatForm) {
            chatForm.addEventListener('submit', (e) => this.handleChatSubmit(e));
        }
        
        // Quick question buttons
        const quickButtons = document.querySelectorAll('.quick-btn');
        quickButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleQuickQuestion(e));
        });
        
        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.handleLogout());
        }
    }
    
    showSignupPage() {
        document.getElementById('signup-page').classList.add('active');
        document.getElementById('chatbot-page').classList.remove('active');
    }
    
    showChatPage() {
        document.getElementById('signup-page').classList.remove('active');
        document.getElementById('chatbot-page').classList.add('active');
        
        if (this.currentUser) {
            document.getElementById('user-email').textContent = this.currentUser.email;
        }
        
        // Load chat history
        this.loadChatHistory();
        
        // Set current time for welcome message
        this.updateMessageTime();
    }
    
    async handleSignup(e) {
        e.preventDefault();
        
        const emailInput = document.getElementById('email');
        const email = emailInput.value.trim();
        const errorMessage = document.getElementById('error-message');
        const signupBtn = document.getElementById('signup-btn');
        const btnText = signupBtn.querySelector('.btn-text');
        const loadingSpinner = signupBtn.querySelector('.loading-spinner');
        
        // Reset error message
        errorMessage.style.display = 'none';
        
        // Validate email
        if (!this.isValidEmail(email)) {
            this.showError('Please enter a valid email address');
            return;
        }
        
        // Show loading state
        signupBtn.disabled = true;
        btnText.style.display = 'none';
        loadingSpinner.style.display = 'inline';
        
        // Simulate API call delay
        await this.delay(1500);
        
        try {
            // Save user data
            this.currentUser = {
                email: email,
                joinedAt: new Date().toISOString()
            };
            
            localStorage.setItem('gameBotUser', JSON.stringify(this.currentUser));
            
            // Show success and redirect to chat
            this.showChatPage();
            
        } catch (error) {
            this.showError('Something went wrong. Please try again.');
        } finally {
            // Reset button state
            signupBtn.disabled = false;
            btnText.style.display = 'inline';
            loadingSpinner.style.display = 'none';
        }
    }
    
    async handleChatSubmit(e) {
        e.preventDefault();
        
        const chatInput = document.getElementById('chat-input');
        const message = chatInput.value.trim();
        
        if (!message || this.isTyping) return;
        
        // Add user message
        this.addMessage(message, 'user');
        chatInput.value = '';
        
        // Hide quick questions after first message
        this.hideQuickQuestions();
        
        // Show typing indicator and get bot response
        await this.getBotResponse(message);
    }
    
    handleQuickQuestion(e) {
        const question = e.target.dataset.question;
        document.getElementById('chat-input').value = question;
        
        // Trigger form submission
        document.getElementById('chat-form').dispatchEvent(new Event('submit'));
    }
    
    handleLogout() {
        localStorage.removeItem('gameBotUser');
        localStorage.removeItem('gameBotChatHistory');
        this.currentUser = null;
        this.chatHistory = [];
        this.showSignupPage();
        
        // Reset chat messages
        const chatMessages = document.getElementById('chat-messages');
        chatMessages.innerHTML = `
            <div class="message bot-message">
                <div class="message-content">
                    <p>🎮 Welcome to Game Bot AI! I'm here to help you discover amazing games, get recommendations, and chat about all things gaming. What would you like to know about games today?</p>
                    <span class="message-time"></span>
                </div>
            </div>
        `;
        
        // Show quick questions again
        document.getElementById('quick-questions').style.display = 'block';
    }
    
    addMessage(content, type) {
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        const currentTime = new Date().toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        messageDiv.innerHTML = `
            <div class="message-content">
                <p>${content}</p>
                <span class="message-time">${currentTime}</span>
            </div>
        `;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        // Save to chat history
        this.chatHistory.push({
            content: content,
            type: type,
            timestamp: new Date().toISOString()
        });
        
        this.saveChatHistory();
    }
    
    async getBotResponse(userMessage) {
        this.isTyping = true;
        
        // Show typing indicator
        this.showTypingIndicator();
        
        // Simulate thinking time
        await this.delay(1000 + Math.random() * 2000);
        
        // Remove typing indicator
        this.hideTypingIndicator();
        
        // Generate response
        const response = this.generateGameResponse(userMessage);
        this.addMessage(response, 'bot');
        
        this.isTyping = false;
    }
    
    generateGameResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        // Game recommendations based on keywords
        if (lowerMessage.includes('recommend') || lowerMessage.includes('suggest')) {
            return "I'd recommend some great games! For action: Call of Duty, Apex Legends. For RPG: The Witcher 3, Skyrim. For strategy: Civilization VI, Age of Empires. What genre interests you most?";
        }
        
        if (lowerMessage.includes('fps') || lowerMessage.includes('shooter')) {
            return "Great FPS games include: Counter-Strike 2, Valorant, Call of Duty series, Battlefield series, and Overwatch 2. Which type of shooter do you prefer - tactical, battle royale, or arcade?";
        }
        
        if (lowerMessage.includes('rpg') || lowerMessage.includes('role playing')) {
            return "Amazing RPGs to try: The Witcher 3, Cyberpunk 2077, Elden Ring, Final Fantasy series, and Baldur's Gate 3. Do you prefer fantasy or sci-fi settings?";
        }
        
        if (lowerMessage.includes('strategy')) {
            return "Top strategy games: Civilization VI, Total War series, StarCraft II, Age of Empires IV, and Europa Universalis IV. Are you interested in turn-based or real-time strategy?";
        }
        
        if (lowerMessage.includes('indie')) {
            return "Fantastic indie games: Hades, Celeste, Hollow Knight, Stardew Valley, and Among Us. What type of indie experience are you looking for?";
        }
        
        if (lowerMessage.includes('multiplayer')) {
            return "Great multiplayer games: Fortnite, Minecraft, Rocket League, Fall Guys, and League of Legends. Do you prefer competitive or cooperative multiplayer?";
        }
        
        if (lowerMessage.includes('trending') || lowerMessage.includes('popular')) {
            return "Currently trending games include: Baldur's Gate 3, Starfield, Spider-Man 2, Alan Wake 2, and Super Mario Bros. Wonder. Which platform do you game on?";
        }
        
        if (lowerMessage.includes('horror') || lowerMessage.includes('scary')) {
            return "Spine-chilling horror games: Resident Evil 4 Remake, Dead Space Remake, Phasmophobia, The Dark Pictures Anthology, and Outlast series. How scary can you handle?";
        }
        
        if (lowerMessage.includes('racing') || lowerMessage.includes('driving')) {
            return "Exciting racing games: Forza Horizon 5, Gran Turismo 7, F1 23, Need for Speed Heat, and Dirt Rally 2.0. Do you prefer arcade or simulation racing?";
        }
        
        // Default response
        return "I'm your gaming assistant! I can help you discover new games, get recommendations, learn about different genres, and find games that match your preferences. What kind of gaming experience are you looking for today?";
    }
    
    showTypingIndicator() {
        const chatMessages = document.getElementById('chat-messages');
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot-message typing-message';
        typingDiv.innerHTML = `
            <div class="message-content">
                <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        
        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    hideTypingIndicator() {
        const typingMessage = document.querySelector('.typing-message');
        if (typingMessage) {
            typingMessage.remove();
        }
    }
    
    hideQuickQuestions() {
        const quickQuestions = document.getElementById('quick-questions');
        if (quickQuestions && this.chatHistory.length > 1) {
            quickQuestions.style.display = 'none';
        }
    }
    
    loadChatHistory() {
        const savedHistory = localStorage.getItem('gameBotChatHistory');
        if (savedHistory) {
            this.chatHistory = JSON.parse(savedHistory);
            
            // Clear existing messages except welcome
            const chatMessages = document.getElementById('chat-messages');
            const welcomeMessage = chatMessages.querySelector('.message');
            chatMessages.innerHTML = '';
            chatMessages.appendChild(welcomeMessage);
            
            // Restore chat history
            this.chatHistory.forEach(msg => {
                if (msg.type !== 'welcome') {
                    this.addMessageFromHistory(msg);
                }
            });
            
            this.hideQuickQuestions();
        }
    }
    
    addMessageFromHistory(msg) {
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${msg.type}-message`;
        
        const time = new Date(msg.timestamp).toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        messageDiv.innerHTML = `
            <div class="message-content">
                <p>${msg.content}</p>
                <span class="message-time">${time}</span>
            </div>
        `;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    saveChatHistory() {
        localStorage.setItem('gameBotChatHistory', JSON.stringify(this.chatHistory));
    }
    
    updateMessageTime() {
        const welcomeTime = document.querySelector('.message-time');
        if (welcomeTime && !welcomeTime.textContent) {
            welcomeTime.textContent = new Date().toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        }
    }
    
    showError(message) {
        const errorElement = document.getElementById('error-message');
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }
    
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new GameBotAI();
});
