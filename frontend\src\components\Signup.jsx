import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import './Auth.css';

const API_BASE_URL = 'http://localhost:5000/api';

function Signup({ onLogin }) {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await axios.post(`${API_BASE_URL}/register`, {
        email: email.trim()
      });

      if (response.data.token) {
        onLogin(response.data.token, response.data.user);
      }
    } catch (error) {
      setError(error.response?.data?.error || 'Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <h2>🎮 Join Game Bot AI</h2>
          <p>Sign up with your email to start chatting about games!</p>
        </div>

        <form onSubmit={handleSubmit} className="auth-form">
          <div className="form-group">
            <label htmlFor="email">Email Address</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              required
              disabled={loading}
            />
          </div>

          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          <button 
            type="submit" 
            className="auth-button"
            disabled={loading || !email.trim()}
          >
            {loading ? 'Creating Account...' : 'Sign Up'}
          </button>
        </form>

        <div className="auth-footer">
          <p>
            Already have an account?{' '}
            <Link to="/login" className="auth-link">
              Login here
            </Link>
          </p>
        </div>

        <div className="features-preview">
          <h3>What you'll get:</h3>
          <ul>
            <li>🎯 Personalized game recommendations</li>
            <li>🔍 Game discovery and reviews</li>
            <li>💬 Chat about any game genre</li>
            <li>📊 Gaming trends and news</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

export default Signup;
